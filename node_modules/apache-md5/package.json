{"name": "apache-md5", "description": "Node.js module for Apache style password encryption using md5.", "version": "1.1.8", "author": "<PERSON><PERSON><PERSON> (http://github.com/gevorg)", "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://github.com/gevorg/apache-md5", "repository": {"type": "git", "url": "http://github.com/gevorg/apache-md5.git"}, "main": "./src/index.js", "typings": "./src/apache-md5.d.ts", "licenses": [{"type": "MIT", "url": "http://github.com/gevorg/apache-md5/blob/master/LICENSE"}], "license": "MIT", "bugs": {"url": "http://github.com/gevorg/apache-md5/issues"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^3.1.2", "mocha": "^7.0.1", "prettier": "^1.19.1"}, "engines": {"node": ">=8"}, "scripts": {"test": "mocha", "pretest": "eslint --ignore-path .gitignore ."}, "keywords": ["apache", "md5", "password", "htpasswd"]}