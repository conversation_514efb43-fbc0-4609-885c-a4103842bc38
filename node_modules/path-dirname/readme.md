# path-dirname [![Build Status](https://travis-ci.org/es128/path-dirname.svg?branch=master)](https://travis-ci.org/es128/path-dirname)

> Node.js [`path.dirname()`](https://nodejs.org/api/path.html#path_path_dirname_path) [ponyfill](https://ponyfill.com)

This was needed in order to expose `path.posix.dirname()` on Node.js v0.10

## Install

```
$ npm install --save path-dirname
```


## Usage

```js
const pathDirname = require('path-dirname');

pathDirname('/home/<USER>');
//=> '/home'
pathDirname('C:\\Users\\<USER>\\Users'
pathDirname('foo');
//=> '.'
pathDirname('foo/bar');
//=> 'foo'

//Using posix version for consistent output when dealing with glob escape chars
pathDirname.win32('C:\\Users\\<USER>\\*bar');
//=> 'C:\\Users\\<USER>\\Users\\foo/\\*bar');
//=> 'C:\\Users\\<USER>