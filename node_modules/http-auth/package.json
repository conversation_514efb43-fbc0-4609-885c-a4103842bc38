{"name": "http-auth", "description": "Node.js package for HTTP basic and digest access authentication.", "version": "3.1.3", "author": "<PERSON><PERSON><PERSON> (http://github.com/gevorg)", "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://http-auth.info", "repository": {"type": "git", "url": "http://github.com/http-auth/http-auth.git"}, "main": "./src/http-auth.js", "licenses": [{"type": "MIT", "url": "http://github.com/http-auth/http-auth/blob/master/LICENSE"}], "license": "MIT", "bugs": {"url": "http://github.com/http-auth/http-auth/issues"}, "dependencies": {"apache-crypt": "^1.1.2", "apache-md5": "^1.0.6", "bcryptjs": "^2.3.0", "uuid": "^3.0.0"}, "devDependencies": {"chai": "^3.5.0", "express": "^4.13.4", "http-proxy": "^1.13.3", "koa": "^1.2.0", "hapi": "^15.0.3", "mocha": "^3.1.2", "passport": "^0.3.2", "request": "^2.72.0"}, "engines": {"node": ">=4.6.1"}, "scripts": {"test": "mocha"}, "keywords": ["http", "basic", "digest", "access", "authentication"]}