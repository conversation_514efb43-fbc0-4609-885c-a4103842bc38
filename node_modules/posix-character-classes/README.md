# posix-character-classes [![NPM version](https://img.shields.io/npm/v/posix-character-classes.svg?style=flat)](https://www.npmjs.com/package/posix-character-classes) [![NPM monthly downloads](https://img.shields.io/npm/dm/posix-character-classes.svg?style=flat)](https://npmjs.org/package/posix-character-classes)  [![NPM total downloads](https://img.shields.io/npm/dt/posix-character-classes.svg?style=flat)](https://npmjs.org/package/posix-character-classes) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/posix-character-classes.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/posix-character-classes)

> POSIX character classes for creating regular expressions.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save posix-character-classes
```

Install with [yarn](https://yarnpkg.com):

```sh
$ yarn add posix-character-classes
```

## Usage

```js
var posix = require('posix-character-classes');
console.log(posix.alpha);
//=> 'A-Za-z'
```

## POSIX Character classes

The POSIX standard supports the following classes or categories of charactersh (note that classes must be defined within brackets)<sup class="footnote-ref"><a href="#fn1" id="fnref1">[1]</a></sup>:

| **POSIX class** | **Equivalent to** | **Matches** | 
| --- | --- | --- |
| `[:alnum:]` | `[A-Za-z0-9]` | digits, uppercase and lowercase letters |
| `[:alpha:]` | `[A-Za-z]` | upper- and lowercase letters |
| `[:ascii:]` | `[\x00-\x7F]` | ASCII characters |
| `[:blank:]` | `[ \t]` | space and TAB characters only |
| `[:cntrl:]` | `[\x00-\x1F\x7F]` | Control characters |
| `[:digit:]` | `[0-9]` | digits |
| `[:graph:]` | `[^[:cntrl:]]` | graphic characters (all characters which have graphic representation) |
| `[:lower:]` | `[a-z]` | lowercase letters |
| `[:print:]` | `[[:graph] ]` | graphic characters and space |
| `[:punct:]` | ``[-!"#$%&'()*+,./:;<=>?@[]^_`{ | }~]`` | all punctuation characters (all graphic characters except letters and digits) |
| `[:space:]` | `[ \t\n\r\f\v]` | all blank (whitespace) characters, including spaces, tabs, new lines, carriage returns, form feeds, and vertical tabs |
| `[:upper:]` | `[A-Z]` | uppercase letters |
| `[:word:]` | `[A-Za-z0-9_]` | word characters |
| `[:xdigit:]` | `[0-9A-Fa-f]` | hexadecimal digits |

## Examples

* `a[[:digit:]]b` matches `a0b`, `a1b`, ..., `a9b`.
* `a[:digit:]b` is invalid, character classes must be enclosed in brackets
* `[[:digit:]abc]` matches any digit, as well as `a`, `b`, and `c`.
* `[abc[:digit:]]` is the same as the previous, matching any digit, as well as `a`, `b`, and `c`
* `[^ABZ[:lower:]]` matches any character except lowercase letters, `A`, `B`, and `Z`.

## About

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.5.0, on April 20, 2017._

<hr class="footnotes-sep">
<section class="footnotes">
<ol class="footnotes-list">
<li id="fn1"  class="footnote-item">table and examples are based on the WikiBooks page for [Regular Expressions/POSIX Basic Regular Expressions](https://en.wikibooks.org/wiki/Regular_Expressions/POSIX_Basic_Regular_Expressions), which is available under the [Creative Commons Attribution-ShareAlike License](https://creativecommons.org/licenses/by-sa/3.0/). <a href="#fnref1" class="footnote-backref">↩</a>

</li>
</ol>
</section>