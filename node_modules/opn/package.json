{"name": "opn", "version": "6.0.0", "description": "Open stuff like URLs, files, executables. Cross-platform.", "license": "MIT", "repository": "sindresorhus/open", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo"}, "files": ["index.js", "xdg-open"], "keywords": ["app", "open", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "dependencies": {"is-wsl": "^1.1.0"}, "devDependencies": {"ava": "^1.4.0", "xo": "^0.24.0"}}