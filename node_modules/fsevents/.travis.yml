language: objective-c


env:
  global:
    - secure: "gve1nkeKkwFEG1VAT3i+JwYyAdF0gKXwKx0uxbkBTsmm2M+0MDusohQdFLoEIkSIFktXBIDefoa7iGpLKRfG2VsZLpwJgnvnD0HqbnuR+k+W+bu7BHt4CAaR6GTllsDCjyq9zNyhUThzSnf2WNIpOEF5kHspZlbGfawURuUJH/U="
    - secure: "jqVpmWxxBVXu2X8+XJMpKH0cooc2EKz9xKL2znBfYdNafJORSXcFAVbjCX5mZmVDcgIMwDtm2+gIG4P73hzJ2e3S+y2Z9ROJGyXHa3AxUTvXHQsxqzH8coHHqB8vTvfr0t2O5aKfpvpICpSea39r0hzNoMv6Ie5SwBdqj1YY9K0="
  matrix:
    - NODE_VERSION="v13"
    - NODE_VERSION="v12"
    - NODE_VERSION="v11"
    - NODE_VERSION="v10"
    - NODE_VERSION="v9"
    - NODE_VERSION="v8"
    - NODE_VERSION="v7"
    - NODE_VERSION="v6"

before_install:

  - echo $TRAVIS_OS_NAME

  # commit
  # ------------------------
  # The commit message is used to determine the whether to manually
  # invoke a binary publish

  - COMMIT_MESSAGE=$(git show -s --format=%B $TRAVIS_COMMIT | tr -d '\n')

  # node
  # ------------------------

  - export PATH=./node_modules/.bin/:$PATH
  - rm -rf ~/.nvm && git clone --depth 1 https://github.com/creationix/nvm.git ~/.nvm
  - source ~/.nvm/nvm.sh
  - nvm install $NODE_VERSION
  - nvm use $NODE_VERSION
  - npm install -g npm@3
  - node --version
  - npm --version
  - nvm --version

  # publish dependencies
  # ------------------------

  - npm install node-gyp -g
  - npm install aws-sdk

install:

  # in the first instance we build from source to create the initial binary
  # which can then be used to create a package

  - npm install --build-from-source
  - npm test

before_script:

  # Detemine if a publish is required.
  #
  # a) we are building a tag
  # b) we put [publish binary] in the commit message

  - PUBLISH_BINARY=false

  - if [[ $TRAVIS_BRANCH == `git describe --tags --always HEAD` ]]; then PUBLISH_BINARY=true; fi;       # a
  - if test "${COMMIT_MESSAGE#*'[publish binary]'}" != "$COMMIT_MESSAGE"; then PUBLISH_BINARY=true; fi; # b

  # package & publish
  # ------------------------

  - if [[ $PUBLISH_BINARY == true ]]; then ./node_modules/.bin/node-pre-gyp package publish; fi;

  # clean-up
  # ------------------------

  - ./node_modules/.bin/node-pre-gyp clean
  - node-gyp clean

script:

  # validate
  # ------------------------
  # Post publishing a release verify that installing will pull down latest
  # binary from remote host

  - INSTALL_RESULT=0
  - if [[ $PUBLISH_BINARY == true ]]; then INSTALL_RESULT=$(npm install --fallback-to-build=false > /dev/null)$? || true; fi;

  - ./node_modules/.bin/node-pre-gyp clean

  # failure?
  # ------------------------
  # if install returned non zero (errored) then we first unpublish and then
  # call false so travis will bail at this line.

  - if [[ $INSTALL_RESULT != 0 ]]; then ./node_modules/.bin/node-pre-gyp unpublish; fi;
  - if [[ $INSTALL_RESULT != 0 ]]; then echo "returned $INSTALL_RESULT";false; fi;

after_success:

  # display all published binaries

 - npm run node-pre-gyp info
