{"name": "split", "version": "0.3.3", "license": "MIT", "description": "split a Text Stream into a Line Stream", "homepage": "http://github.com/dominictarr/split", "repository": {"type": "git", "url": "git://github.com/dominictarr/split.git"}, "dependencies": {"through": "2"}, "devDependencies": {"asynct": "*", "event-stream": "~3.0.2", "it-is": "1", "stream-spec": "~0.2", "ubelt": "~2.9", "string-to-stream": "~1.0.0"}, "scripts": {"test": "asynct test/"}, "author": "<PERSON> <<EMAIL>> (http://bit.ly/dominictarr)", "optionalDependencies": {}, "engines": {"node": "*"}}