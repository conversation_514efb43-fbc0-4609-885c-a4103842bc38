/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: #c8102e;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.2rem;
}

.logo p {
    color: #666;
    font-size: 0.9rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #c8102e;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 150px 0 100px;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1rem;
}

.hero-content h2 {
    font-size: 1.8rem;
    color: #c8102e;
    margin-bottom: 1.5rem;
}

.hero-tagline {
    font-size: 1.3rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    cursor: pointer;
    display: inline-block;
}

.btn-primary {
    background: #c8102e;
    color: white;
}

.btn-primary:hover {
    background: #a00d25;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #c8102e;
    border: 2px solid #c8102e;
}

.btn-secondary:hover {
    background: #c8102e;
    color: white;
}

.hero-image {
    text-align: center;
}

.placeholder-image {
    width: 300px;
    height: 400px;
    background: #ddd;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.2rem;
    color: #666;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 100px 0;
    background: #fff;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text h3 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.credentials {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.credential {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.credential h4 {
    color: #c8102e;
    margin-bottom: 0.5rem;
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat h3 {
    font-size: 2.5rem;
    color: #c8102e;
    margin-bottom: 0.5rem;
}

.stat p {
    color: #666;
    font-weight: 500;
}

/* Services Section */
.services {
    padding: 100px 0;
    background: #f8f9fa;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.service-card p {
    color: #666;
    line-height: 1.6;
}

/* Service Areas Section */
.service-areas {
    padding: 100px 0;
    background: #fff;
}

.areas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.area-card {
    background: linear-gradient(135deg, #c8102e, #a00d25);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    transition: transform 0.3s;
}

.area-card:hover {
    transform: translateY(-3px);
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item h3 {
    color: #c8102e;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #666;
    font-size: 1.1rem;
}

.contact-item a {
    color: #666;
    text-decoration: none;
}

.contact-item a:hover {
    color: #c8102e;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #c8102e;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #c8102e;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: #c8102e;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-menu.active {
        display: flex;
    }

    .hamburger {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content h2 {
        font-size: 1.5rem;
    }

    .hero-tagline {
        font-size: 1.1rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .about-stats {
        flex-direction: row;
        justify-content: center;
    }

    .credentials {
        flex-direction: column;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .areas-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-header h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 120px 0 60px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content h2 {
        font-size: 1.3rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .areas-grid {
        grid-template-columns: 1fr;
    }

    .about-stats {
        flex-direction: column;
    }

    .placeholder-image {
        width: 250px;
        height: 320px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s, transform 0.6s;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}